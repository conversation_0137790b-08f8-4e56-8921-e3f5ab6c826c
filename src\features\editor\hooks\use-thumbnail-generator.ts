import { useCallback, useRef } from "react";
import debounce from "lodash.debounce";
import { useQueryClient } from "@tanstack/react-query";

import { Editor } from "@/features/editor/types";
import { client } from "@/lib/hono";

interface UseThumbnailGeneratorProps {
  editor: Editor | undefined;
  projectId: string;
  onThumbnailGenerated?: (thumbnailUrl: string) => void;
}

export const useThumbnailGenerator = ({
  editor,
  projectId,
  onThumbnailGenerated,
}: UseThumbnailGeneratorProps) => {
  const queryClient = useQueryClient();
  const lastThumbnailRef = useRef<string | null>(null);

  const generateThumbnail = useCallback(async () => {
    if (!editor) {
      console.warn("No editor available for thumbnail generation");
      return;
    }

    if (!editor.canvas) {
      console.warn("No canvas available for thumbnail generation");
      return;
    }

    try {
      console.log("Generating thumbnail for project:", projectId);

      // Generate thumbnail data URL with smaller size for testing
      const thumbnailDataUrl = editor.generateThumbnail({
        width: 300,
        height: 200,
        quality: 0.8,
      });

      if (!thumbnailDataUrl) {
        console.error("Failed to generate thumbnail data URL");
        return;
      }

      console.log("Generated thumbnail data URL length:", thumbnailDataUrl.length);
      console.log("Thumbnail data URL preview:", thumbnailDataUrl.substring(0, 100) + "...");

      // Test if data URL is valid
      const isValidDataUrl = thumbnailDataUrl.startsWith('data:image/');
      console.log("Is valid data URL:", isValidDataUrl);

      // Test if we can create an image from it
      const testImg = new Image();
      testImg.onload = () => console.log("Data URL is valid image");
      testImg.onerror = (e) => console.error("Data URL is invalid:", e);
      testImg.src = thumbnailDataUrl;

      // Skip if thumbnail hasn't changed significantly
      if (lastThumbnailRef.current === thumbnailDataUrl) {
        console.log("Thumbnail unchanged, skipping update");
        return;
      }

      lastThumbnailRef.current = thumbnailDataUrl;
      console.log("Thumbnail generated, updating project...");

      // For now, let's store the thumbnail as a data URL directly in the database
      // This is simpler and doesn't require external file uploads
      // In production, you might want to upload to a CDN

      // Update project with thumbnail data URL
      console.log("Sending PATCH request with thumbnailUrl length:", thumbnailDataUrl.length);
      const response = await client.api.projects[":id"].$patch({
        param: { id: projectId },
        json: {
          thumbnailUrl: thumbnailDataUrl,
        },
      });
      console.log("PATCH response status:", response.status);

      if (response.ok) {
        console.log("Thumbnail updated successfully");
        const responseData = await response.json();
        console.log("Response data:", responseData);

        // Invalidate projects query to refresh the UI
        console.log("Invalidating queries after thumbnail update...");
        queryClient.invalidateQueries({ queryKey: ["projects"] });

        // Also invalidate the individual project query
        queryClient.invalidateQueries({ queryKey: ["project", { id: projectId }] });

        // Force refetch of projects data
        queryClient.refetchQueries({ queryKey: ["projects"] });

        console.log("Query invalidation completed");

        onThumbnailGenerated?.(thumbnailDataUrl);
      } else {
        console.error("Failed to update thumbnail:", response.status, response.statusText);
        const errorText = await response.text();
        console.error("Error response:", errorText);
      }
    } catch (error) {
      console.error("Error generating thumbnail:", error);
    }
  }, [editor, projectId, onThumbnailGenerated]);

  // Debounced version to avoid too frequent thumbnail generation
  const debouncedGenerateThumbnail = useCallback(
    debounce(generateThumbnail, 2000), // Generate thumbnail 2 seconds after last change
    [generateThumbnail]
  );

  const forceRegenerateThumbnail = useCallback(async () => {
    // Reset the last thumbnail to force regeneration
    lastThumbnailRef.current = null;
    await generateThumbnail();
  }, [generateThumbnail]);

  return {
    generateThumbnail,
    debouncedGenerateThumbnail,
    forceRegenerateThumbnail,
  };
};

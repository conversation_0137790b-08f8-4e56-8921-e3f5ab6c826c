import Image from "next/image";
import { formatDistanceToNow } from "date-fns";
import {
  CopyIcon,
  FileIcon,
  Globe,
  Lock,
  MoreHorizontal,
  Settings,
  Trash
} from "lucide-react";

import {
  DropdownMenuContent,
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ProjectCardProps {
  id: string;
  name: string;
  width: number;
  height: number;
  thumbnailUrl?: string | null;
  updatedAt: Date;
  isPublic?: boolean;
  isCustomizable?: boolean;
  onClick: () => void;
  onCopy: (id: string) => void;
  onDelete: (id: string) => void;
  onTogglePublic: (id: string, isPublic: boolean) => void;
  onConfigureTemplate: (id: string) => void;
  copyLoading?: boolean;
  deleteLoading?: boolean;
  togglePublicLoading?: boolean;
}

export const ProjectCard = ({
  id,
  name,
  width,
  height,
  thumbnailUrl,
  updatedAt,
  isPublic = false,
  isCustomizable = false,
  onClick,
  onCopy,
  onDelete,
  onTogglePublic,
  onConfigureTemplate,
  copyLoading = false,
  deleteLoading = false,
  togglePublicLoading = false,
}: ProjectCardProps) => {
  // Debug thumbnail data
  console.log("ProjectCard render:", {
    name,
    thumbnailUrl: thumbnailUrl ? `HAS_THUMBNAIL (${thumbnailUrl.length} chars)` : "NO_THUMBNAIL",
    thumbnailPreview: thumbnailUrl ? thumbnailUrl.substring(0, 50) + "..." : null
  });

  return (
    <div className="group flex flex-col space-y-2">
      {/* Thumbnail/Preview */}
      <div
        onClick={onClick}
        className="relative w-full cursor-pointer overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.03] hover:border-purple-200"
        style={{ aspectRatio: `${width}/${height}` }}
      >
        {thumbnailUrl ? (
          <Image
            src={thumbnailUrl}
            alt={name}
            fill
            className="object-contain bg-white"
            onError={(e) => {
              console.error("Thumbnail failed to load:", thumbnailUrl);
              console.error("Error:", e);
            }}
            onLoad={() => {
              console.log("Thumbnail loaded successfully:", thumbnailUrl?.substring(0, 50) + "...");
            }}
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
            quality={100}
            priority={false}
            unoptimized={true}
            style={{
              imageRendering: 'crisp-edges',
            }}
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gray-50">
            <FileIcon className="h-12 w-12 text-gray-400" />
          </div>
        )}

        {/* Public indicator */}
        {isPublic && (
          <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm">
            <Globe className="h-3 w-3" />
            <span>Public</span>
          </div>
        )}

        {/* Customizable indicator */}
        {isCustomizable && (
          <div className="absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm">
            <Settings className="h-3 w-3" />
            <span>Template</span>
          </div>
        )}

        {/* Hover overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/0 to-black/0 transition-all duration-200 group-hover:from-black/5 group-hover:to-black/0 rounded-xl" />

        {/* Project info overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-3 opacity-0 transition-all duration-200 group-hover:opacity-100 rounded-b-xl">
          <p className="text-xs font-semibold text-white truncate">
            {width} × {height} px
          </p>
        </div>
      </div>

      {/* Project details */}
      <div className="flex items-center justify-between px-1">
        <div className="flex-1 min-w-0">
          <h3
            onClick={onClick}
            className="cursor-pointer font-semibold text-sm text-gray-900 truncate hover:text-purple-600 transition-colors leading-tight"
          >
            {name}
          </h3>
          <p className="text-xs text-gray-500 font-medium">
            {formatDistanceToNow(updatedAt, { addSuffix: true })}
          </p>
        </div>

        {/* Actions dropdown */}
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-gray-100 rounded-lg"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              className="cursor-pointer"
              disabled={copyLoading}
              onClick={(e) => {
                e.stopPropagation();
                onCopy(id);
              }}
            >
              <CopyIcon className="h-4 w-4 mr-2" />
              Make a copy
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer"
              disabled={togglePublicLoading}
              onClick={(e) => {
                e.stopPropagation();
                onTogglePublic(id, !isPublic);
              }}
            >
              {isPublic ? (
                <>
                  <Lock className="h-4 w-4 mr-2" />
                  Make private
                </>
              ) : (
                <>
                  <Globe className="h-4 w-4 mr-2" />
                  Make public
                </>
              )}
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onConfigureTemplate(id);
              }}
            >
              <Settings className="h-4 w-4 mr-2" />
              Template Settings
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer text-red-600 focus:text-red-600"
              disabled={deleteLoading}
              onClick={(e) => {
                e.stopPropagation();
                onDelete(id);
              }}
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

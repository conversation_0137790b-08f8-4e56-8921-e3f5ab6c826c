import { Search } from "lucide-react"
import { User<PERSON>utton } from "@/features/auth/components/user-button"
import { Input } from "@/components/ui/input"

export const Navbar = () => {
  return (
    <nav className="w-full flex items-center p-4 h-[68px] bg-white border-b">
      <div className="flex items-center flex-1 max-w-2xl mx-auto">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search designs, folders, and uploads"
            className="pl-10 bg-gray-50 border-0 focus-visible:ring-1 focus-visible:ring-blue-500 h-10"
          />
        </div>
      </div>
      <div className="ml-4">
        <UserButton />
      </div>
    </nav>
  );
};

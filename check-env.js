// Simple script to check environment variables
console.log("Environment Variables Check:");
console.log("==========================");

const requiredVars = [
  'NEXT_PUBLIC_UNSPLASH_ACCESS_KEY',
  'DATABASE_URL',
  'AUTH_SECRET',
  'UPLOADTHING_SECRET',
  'UPLOADTHING_APP_ID'
];

const optionalVars = [
  'REPLICATE_API_TOKEN',
  'AUTH_GITHUB_ID',
  'AUTH_GITHUB_SECRET',
  'AUTH_GOOGLE_ID',
  'AUTH_GOOGLE_SECRET',
  'STRIPE_SECRET_KEY',
  'STRIPE_PRICE_ID',
  'STRIPE_WEBHOOK_SECRET'
];

console.log("\nRequired Variables:");
requiredVars.forEach(varName => {
  const value = process.env[varName];
  const status = value ? '✅ SET' : '❌ MISSING';
  console.log(`${varName}: ${status}`);
});

console.log("\nOptional Variables:");
optionalVars.forEach(varName => {
  const value = process.env[varName];
  const status = value ? '✅ SET' : '⚠️  NOT SET';
  console.log(`${varName}: ${status}`);
});

console.log("\n==========================");
console.log("If any required variables are missing, please add them to your .env.local file");

import { Hono } from "hono";
import { verifyAuth } from "@hono/auth-js";

import { unsplash } from "@/lib/unsplash";

const DEFAULT_COUNT = 50;
const DEFAULT_COLLECTION_IDS = ["317099"];

// Fallback images when Unsplash is not available
const FALLBACK_IMAGES = [
  {
    id: "fallback-1",
    urls: {
      small: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400",
      regular: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1080",
    },
    alt_description: "Mountain landscape",
    user: {
      name: "Sample User",
      links: { html: "#" }
    },
    links: { html: "#" }
  },
  {
    id: "fallback-2",
    urls: {
      small: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400",
      regular: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1080",
    },
    alt_description: "Forest path",
    user: {
      name: "Sample User",
      links: { html: "#" }
    },
    links: { html: "#" }
  }
];

const app = new Hono()
  .get("/", verifyAuth(), async (c) => {
    try {
      // Check if Unsplash access key is configured
      if (!process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY) {
        console.warn("Unsplash API not configured, using fallback images");
        return c.json({ data: FALLBACK_IMAGES });
      }

      const images = await unsplash.photos.getRandom({
        collectionIds: DEFAULT_COLLECTION_IDS,
        count: DEFAULT_COUNT,
      });

      if (images.errors) {
        console.error("Unsplash API errors:", images.errors);
        console.warn("Falling back to default images");
        return c.json({ data: FALLBACK_IMAGES });
      }

      let response = images.response;

      if (!Array.isArray(response)) {
        response = [response];
      }

      return c.json({ data: response });
    } catch (error) {
      console.error("Images API error:", error);
      console.warn("Falling back to default images");
      return c.json({ data: FALLBACK_IMAGES });
    }
  });

export default app;
